import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../theme/app_theme.dart';

/// Music-themed animations for enhanced user engagement
class MusicAnimations {
  static const Duration beatDuration = Duration(milliseconds: 500);
  static const Duration waveformDuration = Duration(milliseconds: 2000);
}

/// Animated waveform visualization
class CosmicWaveform extends StatefulWidget {
  final double height;
  final int barCount;
  final Color color;
  final bool isPlaying;
  final Duration animationDuration;

  const CosmicWaveform({
    super.key,
    this.height = 40.0,
    this.barCount = 5,
    this.color = AppTheme.purplePrimary,
    this.isPlaying = false,
    this.animationDuration = MusicAnimations.waveformDuration,
  });

  @override
  State<CosmicWaveform> createState() => _CosmicWaveformState();
}

class _CosmicWaveformState extends State<CosmicWaveform>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _controllers = List.generate(widget.barCount, (index) {
      return AnimationController(
        duration: Duration(
          milliseconds: widget.animationDuration.inMilliseconds + (index * 100),
        ),
        vsync: this,
      );
    });

    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.1, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    if (widget.isPlaying) {
      _startAnimation();
    }
  }

  void _startAnimation() {
    for (var controller in _controllers) {
      controller.repeat(reverse: true);
    }
  }

  void _stopAnimation() {
    for (var controller in _controllers) {
      controller.stop();
      controller.reset();
    }
  }

  @override
  void didUpdateWidget(CosmicWaveform oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isPlaying != oldWidget.isPlaying) {
      if (widget.isPlaying) {
        _startAnimation();
      } else {
        _stopAnimation();
      }
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: widget.height,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: List.generate(widget.barCount, (index) {
          return AnimatedBuilder(
            animation: _animations[index],
            builder: (context, child) {
              return Container(
                width: 3.0,
                height: widget.height * _animations[index].value,
                decoration: BoxDecoration(
                  color: widget.color,
                  borderRadius: BorderRadius.circular(1.5),
                  boxShadow: [
                    BoxShadow(
                      color: widget.color.withValues(alpha: 0.3),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ],
                ),
              );
            },
          );
        }),
      ),
    );
  }
}

/// Animated vinyl record for music player
class CosmicVinylRecord extends StatefulWidget {
  final double size;
  final bool isPlaying;
  final Color color;

  const CosmicVinylRecord({
    super.key,
    this.size = 100.0,
    this.isPlaying = false,
    this.color = AppTheme.purplePrimary,
  });

  @override
  State<CosmicVinylRecord> createState() => _CosmicVinylRecordState();
}

class _CosmicVinylRecordState extends State<CosmicVinylRecord>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(_controller);

    if (widget.isPlaying) {
      _controller.repeat();
    }
  }

  @override
  void didUpdateWidget(CosmicVinylRecord oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isPlaying != oldWidget.isPlaying) {
      if (widget.isPlaying) {
        _controller.repeat();
      } else {
        _controller.stop();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _rotationAnimation,
      builder: (context, child) {
        return Transform.rotate(
          angle: _rotationAnimation.value,
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  AppTheme.cosmicDeep,
                  widget.color,
                  AppTheme.cosmicDeep,
                ],
                stops: const [0.0, 0.7, 1.0],
              ),
              boxShadow: [
                BoxShadow(
                  color: widget.color.withValues(alpha: 0.4),
                  blurRadius: 20,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Center(
              child: Container(
                width: widget.size * 0.2,
                height: widget.size * 0.2,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppTheme.cosmicDeep,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Beat-synchronized pulsing animation
class CosmicBeatPulse extends StatefulWidget {
  final Widget child;
  final bool isPlaying;
  final Duration beatInterval;

  const CosmicBeatPulse({
    super.key,
    required this.child,
    this.isPlaying = false,
    this.beatInterval = MusicAnimations.beatDuration,
  });

  @override
  State<CosmicBeatPulse> createState() => _CosmicBeatPulseState();
}

class _CosmicBeatPulseState extends State<CosmicBeatPulse>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.beatInterval,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    if (widget.isPlaying) {
      _controller.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(CosmicBeatPulse oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isPlaying != oldWidget.isPlaying) {
      if (widget.isPlaying) {
        _controller.repeat(reverse: true);
      } else {
        _controller.stop();
        _controller.reset();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: widget.child,
        );
      },
    );
  }
}

/// Animated music note particles
class CosmicMusicNotes extends StatefulWidget {
  final double width;
  final double height;
  final int noteCount;
  final bool isPlaying;

  const CosmicMusicNotes({
    super.key,
    this.width = 200.0,
    this.height = 100.0,
    this.noteCount = 5,
    this.isPlaying = false,
  });

  @override
  State<CosmicMusicNotes> createState() => _CosmicMusicNotesState();
}

class _CosmicMusicNotesState extends State<CosmicMusicNotes>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<Offset>> _positionAnimations;
  late List<Animation<double>> _opacityAnimations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _controllers = List.generate(widget.noteCount, (index) {
      return AnimationController(
        duration: Duration(milliseconds: 2000 + (index * 200)),
        vsync: this,
      );
    });

    _positionAnimations = _controllers.map((controller) {
      return Tween<Offset>(
        begin: Offset(math.Random().nextDouble(), 1.0),
        end: Offset(math.Random().nextDouble(), -0.2),
      ).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeOut,
      ));
    }).toList();

    _opacityAnimations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: controller,
          curve: const Interval(0.0, 0.3),
        ),
      );
    }).toList();

    if (widget.isPlaying) {
      _startAnimation();
    }
  }

  void _startAnimation() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 300), () {
        if (mounted && widget.isPlaying) {
          _controllers[i].repeat();
        }
      });
    }
  }

  void _stopAnimation() {
    for (var controller in _controllers) {
      controller.stop();
      controller.reset();
    }
  }

  @override
  void didUpdateWidget(CosmicMusicNotes oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isPlaying != oldWidget.isPlaying) {
      if (widget.isPlaying) {
        _startAnimation();
      } else {
        _stopAnimation();
      }
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: Stack(
        children: List.generate(widget.noteCount, (index) {
          return AnimatedBuilder(
            animation: _controllers[index],
            builder: (context, child) {
              return Positioned(
                left: _positionAnimations[index].value.dx * widget.width,
                top: _positionAnimations[index].value.dy * widget.height,
                child: Opacity(
                  opacity: _opacityAnimations[index].value,
                  child: Icon(
                    Icons.music_note,
                    color: AppTheme.purplePrimary.withValues(alpha: 0.7),
                    size: 16 + (index * 2),
                  ),
                ),
              );
            },
          );
        }),
      ),
    );
  }
}
