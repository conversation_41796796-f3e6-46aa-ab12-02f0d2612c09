import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:math' as math;

import '../../core/theme/app_theme.dart';
import '../../core/animations/splash_animations.dart';
import '../../core/constants/app_constants.dart';
import '../../core/navigation/app_routes.dart';

/// Psychologically engaging splash screen that creates anticipation and excitement
/// Features smooth animations, music-themed visuals, and cosmic design elements
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late AnimationController _waveformController;
  late Animation<double> _backgroundAnimation;
  late Animation<double> _waveformAnimation;

  bool _logoAnimationComplete = false;
  bool _shouldNavigate = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startSplashSequence();
  }

  void _initializeAnimations() {
    // Background gradient animation
    _backgroundController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _backgroundAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _backgroundController, curve: Curves.easeInOut),
    );

    // Waveform animation for music theme
    _waveformController = AnimationController(
      duration: SplashAnimations.waveformDuration,
      vsync: this,
    );
    _waveformAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _waveformController,
        curve: SplashAnimations.waveformCurve,
      ),
    );
  }

  void _startSplashSequence() {
    // Start background animation immediately
    _backgroundController.forward();

    // Start waveform animation after a delay
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (mounted) {
        _waveformController.repeat(reverse: true);
      }
    });

    // Set minimum splash duration
    Timer(SplashAnimations.totalSplashDuration, () {
      if (mounted) {
        setState(() {
          _shouldNavigate = true;
        });
        _navigateToNextScreen();
      }
    });
  }

  void _onLogoAnimationComplete() {
    setState(() {
      _logoAnimationComplete = true;
    });
  }

  void _navigateToNextScreen() {
    if (_logoAnimationComplete && _shouldNavigate) {
      // Add haptic feedback for premium feel
      HapticFeedback.lightImpact();

      // Navigate to auth wrapper
      Navigator.of(context).pushReplacementNamed(AppRoutes.login);
    }
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _waveformController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedBuilder(
        animation: _backgroundAnimation,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color.lerp(
                    AppTheme.cosmicDeep,
                    AppTheme.cosmicDark,
                    _backgroundAnimation.value,
                  )!,
                  Color.lerp(
                    AppTheme.cosmicDark,
                    AppTheme.purpleDark,
                    _backgroundAnimation.value,
                  )!,
                  Color.lerp(
                    AppTheme.cosmicMedium,
                    AppTheme.cosmicDeep,
                    _backgroundAnimation.value,
                  )!,
                ],
                stops: const [0.0, 0.6, 1.0],
              ),
            ),
            child: SafeArea(
              child: Stack(
                children: [
                  // Floating particles background
                  const Positioned.fill(
                    child: FloatingMusicParticles(
                      particleCount: 20,
                      width: double.infinity,
                      height: double.infinity,
                    ),
                  ),

                  // Animated waveform background
                  Positioned.fill(child: _buildAnimatedWaveform()),

                  // Main content
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Animated logo
                        AnimatedSplashLogo(
                          size: 140,
                          onAnimationComplete: _onLogoAnimationComplete,
                        ),

                        const SizedBox(height: 40),

                        // App name with gradient text
                        AnimatedSplashText(
                          text: AppConstants.appName,
                          style: const TextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                          ),
                          delay: const Duration(milliseconds: 800),
                        ),

                        const SizedBox(height: 16),

                        // App description
                        AnimatedSplashText(
                          text: AppConstants.appDescription,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: AppTheme.cosmicGray.withValues(alpha: 0.9),
                            letterSpacing: 0.5,
                          ),
                          delay: const Duration(milliseconds: 1200),
                          duration: const Duration(milliseconds: 800),
                        ),

                        const SizedBox(height: 60),

                        // Loading indicator with cosmic styling
                        AnimatedSplashText(
                          text: '',
                          delay: const Duration(milliseconds: 1600),
                          duration: const Duration(milliseconds: 400),
                        ),

                        const SizedBox(height: 20),

                        _buildCosmicLoadingIndicator(),
                      ],
                    ),
                  ),

                  // Subtle version info at bottom
                  Positioned(
                    bottom: 40,
                    left: 0,
                    right: 0,
                    child: AnimatedSplashText(
                      text: 'Version ${AppConstants.appVersion}',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.cosmicMuted.withValues(alpha: 0.7),
                        fontWeight: FontWeight.w400,
                      ),
                      delay: const Duration(milliseconds: 2000),
                      duration: const Duration(milliseconds: 600),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAnimatedWaveform() {
    return AnimatedBuilder(
      animation: _waveformAnimation,
      builder: (context, child) {
        return CustomPaint(
          painter: WaveformPainter(
            progress: _waveformAnimation.value,
            color: AppTheme.purplePrimary.withValues(alpha: 0.1),
          ),
          size: Size.infinite,
        );
      },
    );
  }

  Widget _buildCosmicLoadingIndicator() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: const LinearGradient(
          colors: [AppTheme.purplePrimary, AppTheme.electricBlue],
        ),
      ),
      child: const Padding(
        padding: EdgeInsets.all(8.0),
        child: CircularProgressIndicator(
          strokeWidth: 3,
          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.cosmicWhite),
        ),
      ),
    );
  }
}

/// Custom painter for animated waveform background
class WaveformPainter extends CustomPainter {
  final double progress;
  final Color color;

  WaveformPainter({required this.progress, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final path = Path();
    final waveHeight = 30.0;
    final waveLength = size.width / 4;

    // Create multiple wave layers for depth
    for (int layer = 0; layer < 3; layer++) {
      path.reset();
      final layerOffset = layer * 20.0;
      final layerAlpha = (1.0 - layer * 0.3) * progress;

      paint.color = color.withValues(alpha: layerAlpha);

      for (double x = -waveLength; x <= size.width + waveLength; x += 2) {
        final y =
            size.height / 2 +
            layerOffset +
            waveHeight *
                (1 + layer * 0.5) *
                (progress * 0.5 + 0.5) *
                (math.sin((x / waveLength + progress * 2) * 2 * math.pi) +
                    math.sin(
                          (x / (waveLength * 0.7) + progress * 3) * 2 * math.pi,
                        ) *
                        0.5);

        if (x == -waveLength) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }

      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
