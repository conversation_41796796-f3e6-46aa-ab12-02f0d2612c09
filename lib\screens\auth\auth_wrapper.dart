import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../services/auth_service.dart';
import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import 'login_screen.dart';
import '../onboarding/onboarding_screen.dart';

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    final authService = Provider.of<AuthService>(context, listen: false);
    await authService.initialize();

    if (mounted) {
      setState(() {
        _isInitialized = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const AuthLoadingScreen();
    }

    return Consumer<AuthService>(
      builder: (context, authService, child) {
        // If user is logged in, show onboarding or main screen
        if (authService.isLoggedIn) {
          return const OnboardingScreen(); // This will check if onboarding is needed
        }

        // If not logged in, show login screen
        return const LoginScreen();
      },
    );
  }
}

class AuthLoadingScreen extends StatelessWidget {
  const AuthLoadingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.cosmicDeep,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.cosmicDeep,
              AppTheme.cosmicDark,
              AppTheme.cosmicMedium,
            ],
            stops: [0.0, 0.6, 1.0],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App Logo with cosmic styling
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24),
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppTheme.purplePrimary,
                      AppTheme.purpleSecondary,
                      AppTheme.electricBlue,
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.purplePrimary.withValues(alpha: 0.4),
                      blurRadius: 20,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.music_note,
                  size: 60,
                  color: AppTheme.cosmicWhite, // High contrast white
                ),
              ),

              const SizedBox(height: 32),

              // App Name with high contrast
              ShaderMask(
                shaderCallback: (bounds) =>
                    AppTheme.purpleGradient.createShader(bounds),
                child: Text(
                  AppConstants.appName,
                  style: AppTheme.darkTheme.textTheme.displayMedium?.copyWith(
                    color: AppTheme.cosmicWhite, // High contrast white
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Loading indicator with cosmic styling
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  gradient: const LinearGradient(
                    colors: [AppTheme.purplePrimary, AppTheme.electricBlue],
                  ),
                ),
                child: const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppTheme.cosmicWhite,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Loading text with high contrast
              Text(
                'Loading...',
                style: AppTheme.darkTheme.textTheme.bodyLarge?.copyWith(
                  color: AppTheme
                      .cosmicWhite, // High contrast white instead of gray
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
