import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'core/theme/app_theme.dart';
import 'core/constants/app_constants.dart';
import 'core/navigation/app_routes.dart';
import 'models/song.dart';
import 'models/playlist.dart';
import 'models/user.dart';
import 'services/audio_player_service.dart';
import 'services/auth_service.dart';
// import 'services/voice_search_service.dart'; // Temporarily disabled
import 'screens/splash/splash_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive
  await Hive.initFlutter();

  // Register Hive adapters
  Hive.registerAdapter(SongAdapter());
  Hive.registerAdapter(PlaylistAdapter());
  Hive.registerAdapter(UserAdapter());
  Hive.registerAdapter(UserPreferencesAdapter());
  Hive.registerAdapter(RepeatModeAdapter());
  Hive.registerAdapter(AudioQualityAdapter());

  // Open Hive boxes
  await Hive.openBox<Song>(AppConstants.songBoxKey);
  await Hive.openBox<Playlist>(AppConstants.playlistBoxKey);
  await Hive.openBox<User>(AppConstants.userBoxKey);
  await Hive.openBox(AppConstants.settingsBoxKey);

  // Set system UI overlay style with cosmic theme
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: AppTheme.cosmicDeep,
      systemNavigationBarIconBrightness: Brightness.light,
    ),
  );

  runApp(const MuseAIApp());
}

class MuseAIApp extends StatelessWidget {
  const MuseAIApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AudioPlayerService()),
        ChangeNotifierProvider(create: (_) => AuthService()),
        // ChangeNotifierProvider(create: (_) => VoiceSearchService()), // Temporarily disabled
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        theme: AppTheme.darkTheme,
        debugShowCheckedModeBanner: false,
        initialRoute: AppRoutes.splash,
        onGenerateRoute: AppRoutes.generateRoute,
        home: const AppInitializer(),
      ),
    );
  }
}

class AppInitializer extends StatelessWidget {
  const AppInitializer({super.key});

  @override
  Widget build(BuildContext context) {
    return const SplashScreen();
  }
}
