import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import '../theme/app_theme.dart';

/// Central animation controller for managing psychological engagement animations
class CosmicAnimationController {
  static const Duration _defaultDuration = Duration(milliseconds: 300);

  /// Creates a dopamine-triggering success animation
  static void triggerSuccessAnimation(BuildContext context) {
    HapticFeedback.heavyImpact();

    // Show success particles
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.transparent,
      builder: (context) => const SuccessParticleAnimation(),
    );

    // Auto dismiss after animation
    Future.delayed(const Duration(milliseconds: 2000), () {
      if (context.mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }
    });
  }

  /// Creates an engaging loading animation
  static Widget createLoadingAnimation({
    String? message,
    Color color = AppTheme.purplePrimary,
  }) {
    return CosmicLoadingAnimation(message: message, color: color);
  }

  /// Creates a satisfying button press animation
  static Widget createButtonAnimation({
    required Widget child,
    required VoidCallback onPressed,
    Duration duration = _defaultDuration,
  }) {
    return CosmicButtonAnimation(
      onPressed: onPressed,
      duration: duration,
      child: child,
    );
  }
}

/// Success particle animation for dopamine release
class SuccessParticleAnimation extends StatefulWidget {
  const SuccessParticleAnimation({super.key});

  @override
  State<SuccessParticleAnimation> createState() =>
      _SuccessParticleAnimationState();
}

class _SuccessParticleAnimationState extends State<SuccessParticleAnimation>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<Offset>> _positionAnimations;
  late List<Animation<double>> _opacityAnimations;

  final int particleCount = 12;
  final List<Color> colors = [
    AppTheme.purplePrimary,
    AppTheme.purpleSecondary,
    AppTheme.electricBlue,
    AppTheme.cosmicGold,
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimation();
  }

  void _initializeAnimations() {
    _controllers = List.generate(particleCount, (index) {
      return AnimationController(
        duration: Duration(milliseconds: 1500 + (index * 100)),
        vsync: this,
      );
    });

    _scaleAnimations = _controllers.map((controller) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(parent: controller, curve: Curves.elasticOut));
    }).toList();

    _positionAnimations = _controllers.asMap().entries.map((entry) {
      final index = entry.key;
      final controller = entry.value;
      final angle = (index / particleCount) * 2 * math.pi;

      return Tween<Offset>(
        begin: Offset.zero,
        end: Offset(math.cos(angle) * 100, math.sin(angle) * 100),
      ).animate(CurvedAnimation(parent: controller, curve: Curves.easeOut));
    }).toList();

    _opacityAnimations = _controllers.map((controller) {
      return Tween<double>(begin: 1.0, end: 0.0).animate(
        CurvedAnimation(parent: controller, curve: const Interval(0.7, 1.0)),
      );
    }).toList();
  }

  void _startAnimation() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 50), () {
        if (mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Center(
        child: SizedBox(
          width: 300,
          height: 300,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Central success icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: const LinearGradient(
                    colors: [AppTheme.purplePrimary, AppTheme.purpleSecondary],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.purplePrimary.withValues(alpha: 0.5),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.check,
                  color: AppTheme.cosmicWhite,
                  size: 40,
                ),
              ),
              // Particles
              ...List.generate(particleCount, (index) {
                return AnimatedBuilder(
                  animation: _controllers[index],
                  builder: (context, child) {
                    return Transform.translate(
                      offset: _positionAnimations[index].value,
                      child: Transform.scale(
                        scale: _scaleAnimations[index].value,
                        child: Opacity(
                          opacity: _opacityAnimations[index].value,
                          child: Container(
                            width: 8 + (index % 3) * 4,
                            height: 8 + (index % 3) * 4,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: colors[index % colors.length],
                              boxShadow: [
                                BoxShadow(
                                  color: colors[index % colors.length]
                                      .withValues(alpha: 0.5),
                                  blurRadius: 10,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                );
              }),
            ],
          ),
        ),
      ),
    );
  }
}

/// Engaging loading animation with cosmic effects
class CosmicLoadingAnimation extends StatefulWidget {
  final String? message;
  final Color color;

  const CosmicLoadingAnimation({
    super.key,
    this.message,
    this.color = AppTheme.purplePrimary,
  });

  @override
  State<CosmicLoadingAnimation> createState() => _CosmicLoadingAnimationState();
}

class _CosmicLoadingAnimationState extends State<CosmicLoadingAnimation>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _pulseController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(_rotationController);

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _rotationController.repeat();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          AnimatedBuilder(
            animation: _rotationController,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotationAnimation.value,
                child: AnimatedBuilder(
                  animation: _pulseController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              widget.color,
                              widget.color.withValues(alpha: 0.3),
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: widget.color.withValues(alpha: 0.5),
                              blurRadius: 20,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.music_note,
                          color: AppTheme.cosmicWhite,
                          size: 30,
                        ),
                      ),
                    );
                  },
                ),
              );
            },
          ),
          if (widget.message != null) ...[
            const SizedBox(height: 20),
            Text(
              widget.message!,
              style: const TextStyle(
                color: AppTheme.cosmicGray,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Button animation with satisfying feedback
class CosmicButtonAnimation extends StatefulWidget {
  final Widget child;
  final VoidCallback onPressed;
  final Duration duration;

  const CosmicButtonAnimation({
    super.key,
    required this.child,
    required this.onPressed,
    this.duration = const Duration(milliseconds: 300),
  });

  @override
  State<CosmicButtonAnimation> createState() => _CosmicButtonAnimationState();
}

class _CosmicButtonAnimationState extends State<CosmicButtonAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(duration: widget.duration, vsync: this);

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _controller.forward();
    HapticFeedback.lightImpact();
  }

  void _onTapUp(TapUpDetails details) {
    _controller.reverse();
    HapticFeedback.mediumImpact();
    widget.onPressed();
  }

  void _onTapCancel() {
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.purplePrimary.withValues(
                      alpha: _glowAnimation.value * 0.5,
                    ),
                    blurRadius: 20 * _glowAnimation.value,
                    spreadRadius: 2 * _glowAnimation.value,
                  ),
                ],
              ),
              child: widget.child,
            ),
          );
        },
      ),
    );
  }
}
