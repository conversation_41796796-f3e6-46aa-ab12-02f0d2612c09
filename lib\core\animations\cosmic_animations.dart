import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Cosmic-themed animations designed to increase user engagement and retention
/// Based on psychological principles of dopamine-driven design
class CosmicAnimations {
  // Animation durations optimized for user engagement
  static const Duration fastDuration = Duration(milliseconds: 200);
  static const Duration mediumDuration = Duration(milliseconds: 400);
  static const Duration slowDuration = Duration(milliseconds: 600);
  static const Duration delightDuration = Duration(milliseconds: 800);

  // Curves that feel natural and satisfying
  static const Curve satisfyingCurve = Curves.easeOutCubic;
  static const Curve bouncyCurve = Curves.elasticOut;
  static const Curve smoothCurve = Curves.easeInOutCubic;
}

/// Animated button that provides satisfying feedback
class CosmicAnimatedButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final Duration duration;
  final double scaleDown;
  final bool hapticFeedback;

  const CosmicAnimatedButton({
    super.key,
    required this.child,
    this.onTap,
    this.duration = CosmicAnimations.fastDuration,
    this.scaleDown = 0.95,
    this.hapticFeedback = true,
  });

  @override
  State<CosmicAnimatedButton> createState() => _CosmicAnimatedButtonState();
}

class _CosmicAnimatedButtonState extends State<CosmicAnimatedButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: widget.scaleDown,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: CosmicAnimations.satisfyingCurve,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _controller.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _controller.reverse();
    if (widget.hapticFeedback) {
      // Add haptic feedback for satisfying interaction
      // HapticFeedback.lightImpact();
    }
    widget.onTap?.call();
  }

  void _onTapCancel() {
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: widget.child,
          );
        },
      ),
    );
  }
}

/// Floating action animation for delightful micro-interactions
class CosmicFloatingAnimation extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final double floatDistance;

  const CosmicFloatingAnimation({
    super.key,
    required this.child,
    this.duration = const Duration(seconds: 3),
    this.floatDistance = 10.0,
  });

  @override
  State<CosmicFloatingAnimation> createState() => _CosmicFloatingAnimationState();
}

class _CosmicFloatingAnimationState extends State<CosmicFloatingAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _floatAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _floatAnimation = Tween<double>(
      begin: 0.0,
      end: widget.floatDistance,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _floatAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, -_floatAnimation.value),
          child: widget.child,
        );
      },
    );
  }
}

/// Pulsing animation for drawing attention
class CosmicPulseAnimation extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final double minScale;
  final double maxScale;

  const CosmicPulseAnimation({
    super.key,
    required this.child,
    this.duration = const Duration(seconds: 2),
    this.minScale = 0.95,
    this.maxScale = 1.05,
  });

  @override
  State<CosmicPulseAnimation> createState() => _CosmicPulseAnimationState();
}

class _CosmicPulseAnimationState extends State<CosmicPulseAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: widget.minScale,
      end: widget.maxScale,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: widget.child,
        );
      },
    );
  }
}

/// Shimmer loading animation for engaging loading states
class CosmicShimmer extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Color highlightColor;
  final Color baseColor;

  const CosmicShimmer({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 1500),
    this.highlightColor = const Color(0x66FFFFFF),
    this.baseColor = const Color(0x33FFFFFF),
  });

  @override
  State<CosmicShimmer> createState() => _CosmicShimmerState();
}

class _CosmicShimmerState extends State<CosmicShimmer>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _shimmerAnimation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                widget.baseColor,
                widget.highlightColor,
                widget.baseColor,
              ],
              stops: [
                math.max(0.0, _shimmerAnimation.value - 0.3),
                _shimmerAnimation.value,
                math.min(1.0, _shimmerAnimation.value + 0.3),
              ],
            ).createShader(bounds);
          },
          child: widget.child,
        );
      },
    );
  }
}

/// Staggered animation for list items
class CosmicStaggeredAnimation extends StatefulWidget {
  final List<Widget> children;
  final Duration duration;
  final Duration delay;
  final Axis direction;

  const CosmicStaggeredAnimation({
    super.key,
    required this.children,
    this.duration = CosmicAnimations.mediumDuration,
    this.delay = const Duration(milliseconds: 100),
    this.direction = Axis.vertical,
  });

  @override
  State<CosmicStaggeredAnimation> createState() => _CosmicStaggeredAnimationState();
}

class _CosmicStaggeredAnimationState extends State<CosmicStaggeredAnimation>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
      widget.children.length,
      (index) => AnimationController(
        duration: widget.duration,
        vsync: this,
      ),
    );
    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: controller,
          curve: CosmicAnimations.satisfyingCurve,
        ),
      );
    }).toList();

    _startStaggeredAnimation();
  }

  void _startStaggeredAnimation() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(widget.delay * i, () {
        if (mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(widget.children.length, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Transform.translate(
              offset: widget.direction == Axis.vertical
                  ? Offset(0, 50 * (1 - _animations[index].value))
                  : Offset(50 * (1 - _animations[index].value), 0),
              child: Opacity(
                opacity: _animations[index].value,
                child: widget.children[index],
              ),
            );
          },
        );
      }),
    );
  }
}
