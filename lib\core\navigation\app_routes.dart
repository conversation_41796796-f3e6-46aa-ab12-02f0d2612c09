import 'package:flutter/material.dart';

// Main Screens
import '../../screens/main/main_screen.dart';

// Splash Screen
import '../../screens/splash/splash_screen.dart';

// Authentication
import '../../screens/auth/signup_screen.dart';
import '../../screens/auth/auth_wrapper.dart';
import '../../screens/onboarding/onboarding_screen.dart';

// Analytics & Insights
import '../../screens/analytics/listening_analytics_screen.dart';
import '../../screens/analytics/year_in_review_screen.dart';

// Social Features
import '../../screens/social/friends_activity_screen.dart';

// Discovery
import '../../screens/discovery/music_news_screen.dart';
import '../../screens/discovery/concert_events_screen.dart';

// AI Features
import '../../screens/ai/ai_lab_screen.dart';
import '../../screens/ai_playlist/ai_playlist_screen.dart';
import '../../screens/voice/voice_search_screen.dart';

// Player Features
import '../../screens/player/now_playing_screen.dart';
import '../../screens/player/equalizer_screen.dart';
import '../../screens/player/sleep_timer_screen.dart';

// Gamification
import '../../screens/gamification/music_achievements_screen.dart';
import '../../screens/gamification/music_trivia_screen.dart';

// Premium Features
import '../../screens/premium/offline_downloads_screen.dart';
import '../../screens/subscription/subscription_screen.dart';

// Settings & Profile
import '../../screens/settings/settings_screen.dart';
import '../../screens/settings/accessibility_settings_screen.dart';
import '../../screens/profile/user_profile_screen.dart';

// Support
import '../../screens/support/help_faq_screen.dart';

class AppRoutes {
  // Route names
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String signup = '/signup';
  static const String main = '/main';
  static const String home = '/home';
  static const String search = '/search';
  static const String library = '/library';

  // Analytics
  static const String listeningAnalytics = '/analytics/listening';
  static const String yearInReview = '/analytics/year-in-review';

  // Social
  static const String friendsActivity = '/social/friends';

  // Discovery
  static const String musicNews = '/discovery/news';
  static const String concertEvents = '/discovery/concerts';

  // AI Features
  static const String aiLab = '/ai/lab';
  static const String aiPlaylist = '/ai/playlist';
  static const String voiceSearch = '/voice/search';

  // Player
  static const String nowPlaying = '/player/now-playing';
  static const String equalizer = '/player/equalizer';
  static const String sleepTimer = '/player/sleep-timer';

  // Gamification
  static const String achievements = '/gamification/achievements';
  static const String musicTrivia = '/gamification/trivia';

  // Premium
  static const String offlineDownloads = '/premium/downloads';
  static const String subscription = '/subscription';

  // Settings & Profile
  static const String settings = '/settings';
  static const String accessibility = '/settings/accessibility';
  static const String profile = '/profile';

  // Support
  static const String help = '/support/help';

  // Route generator
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AppRoutes.splash:
        return MaterialPageRoute(builder: (_) => const SplashScreen());

      case AppRoutes.onboarding:
        return MaterialPageRoute(builder: (_) => const OnboardingScreen());

      case AppRoutes.login:
        return MaterialPageRoute(builder: (_) => const AuthWrapper());

      case AppRoutes.signup:
        return MaterialPageRoute(builder: (_) => const SignUpScreen());

      case AppRoutes.main:
        return MaterialPageRoute(builder: (_) => const MainScreen());

      // Analytics
      case AppRoutes.listeningAnalytics:
        return MaterialPageRoute(
          builder: (_) => const ListeningAnalyticsScreen(),
        );

      case AppRoutes.yearInReview:
        final year = settings.arguments as int? ?? DateTime.now().year;
        return MaterialPageRoute(
          builder: (_) => YearInReviewScreen(year: year),
        );

      // Social
      case AppRoutes.friendsActivity:
        return MaterialPageRoute(builder: (_) => const FriendsActivityScreen());

      // Discovery
      case AppRoutes.musicNews:
        return MaterialPageRoute(builder: (_) => const MusicNewsScreen());

      case AppRoutes.concertEvents:
        return MaterialPageRoute(builder: (_) => const ConcertEventsScreen());

      // AI Features
      case AppRoutes.aiLab:
        return MaterialPageRoute(builder: (_) => const AILabScreen());

      case AppRoutes.aiPlaylist:
        return MaterialPageRoute(builder: (_) => const AIPlaylistScreen());

      case AppRoutes.voiceSearch:
        return MaterialPageRoute(builder: (_) => const VoiceSearchScreen());

      // Player
      case AppRoutes.nowPlaying:
        return MaterialPageRoute(
          builder: (_) => const NowPlayingScreen(),
          fullscreenDialog: true,
        );

      case AppRoutes.equalizer:
        return MaterialPageRoute(builder: (_) => const EqualizerScreen());

      case AppRoutes.sleepTimer:
        return MaterialPageRoute(builder: (_) => const SleepTimerScreen());

      // Gamification
      case AppRoutes.achievements:
        return MaterialPageRoute(
          builder: (_) => const MusicAchievementsScreen(),
        );

      case AppRoutes.musicTrivia:
        return MaterialPageRoute(builder: (_) => const MusicTriviaScreen());

      // Premium
      case AppRoutes.offlineDownloads:
        return MaterialPageRoute(
          builder: (_) => const OfflineDownloadsScreen(),
        );

      case AppRoutes.subscription:
        return MaterialPageRoute(builder: (_) => const SubscriptionScreen());

      // Settings & Profile
      case AppRoutes.settings:
        return MaterialPageRoute(builder: (_) => const SettingsScreen());

      case AppRoutes.accessibility:
        return MaterialPageRoute(
          builder: (_) => const AccessibilitySettingsScreen(),
        );

      case AppRoutes.profile:
        return MaterialPageRoute(builder: (_) => const UserProfileScreen());

      // Support
      case AppRoutes.help:
        return MaterialPageRoute(builder: (_) => const HelpFAQScreen());

      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            body: Center(child: Text('No route defined for ${settings.name}')),
          ),
        );
    }
  }

  // Navigation helpers
  static void pushNamed(
    BuildContext context,
    String routeName, {
    Object? arguments,
  }) {
    Navigator.pushNamed(context, routeName, arguments: arguments);
  }

  static void pushReplacementNamed(
    BuildContext context,
    String routeName, {
    Object? arguments,
  }) {
    Navigator.pushReplacementNamed(context, routeName, arguments: arguments);
  }

  static void pushNamedAndClearStack(
    BuildContext context,
    String routeName, {
    Object? arguments,
  }) {
    Navigator.pushNamedAndRemoveUntil(
      context,
      routeName,
      (route) => false,
      arguments: arguments,
    );
  }

  static void pop(BuildContext context, [dynamic result]) {
    Navigator.pop(context, result);
  }

  static bool canPop(BuildContext context) {
    return Navigator.canPop(context);
  }
}

// Screen categories for organization
class ScreenCategories {
  static const List<Map<String, dynamic>> analytics = [
    {
      'name': 'Your Stats',
      'route': AppRoutes.listeningAnalytics,
      'icon': 'analytics',
      'description': 'View your listening statistics and insights',
    },
    {
      'name': 'Year in Review',
      'route': AppRoutes.yearInReview,
      'icon': 'calendar_today',
      'description': 'Your annual music summary',
    },
  ];

  static const List<Map<String, dynamic>> social = [
    {
      'name': 'Friends Activity',
      'route': AppRoutes.friendsActivity,
      'icon': 'people',
      'description': 'See what your friends are listening to',
    },
  ];

  static const List<Map<String, dynamic>> discovery = [
    {
      'name': 'Music News',
      'route': AppRoutes.musicNews,
      'icon': 'newspaper',
      'description': 'Latest music news and artist updates',
    },
    {
      'name': 'Concerts & Events',
      'route': AppRoutes.concertEvents,
      'icon': 'event',
      'description': 'Find concerts and music events near you',
    },
  ];

  static const List<Map<String, dynamic>> aiFeatures = [
    {
      'name': 'AI Lab',
      'route': AppRoutes.aiLab,
      'icon': 'science',
      'description': 'Experimental AI music features',
    },
    {
      'name': 'AI Playlist Generator',
      'route': AppRoutes.aiPlaylist,
      'icon': 'auto_awesome',
      'description': 'Generate playlists with AI',
    },
    {
      'name': 'Voice Search',
      'route': AppRoutes.voiceSearch,
      'icon': 'mic',
      'description': 'Search music with your voice',
    },
  ];

  static const List<Map<String, dynamic>> audio = [
    {
      'name': 'Equalizer',
      'route': AppRoutes.equalizer,
      'icon': 'equalizer',
      'description': 'Customize your audio experience',
    },
    {
      'name': 'Sleep Timer',
      'route': AppRoutes.sleepTimer,
      'icon': 'bedtime',
      'description': 'Set a timer to stop music',
    },
  ];

  static const List<Map<String, dynamic>> gamification = [
    {
      'name': 'Achievements',
      'route': AppRoutes.achievements,
      'icon': 'emoji_events',
      'description': 'View your music achievements',
    },
    {
      'name': 'Music Trivia',
      'route': AppRoutes.musicTrivia,
      'icon': 'quiz',
      'description': 'Test your music knowledge',
    },
  ];

  static const List<Map<String, dynamic>> premium = [
    {
      'name': 'Offline Downloads',
      'route': AppRoutes.offlineDownloads,
      'icon': 'download',
      'description': 'Manage your offline music',
    },
  ];

  static const List<Map<String, dynamic>> account = [
    {
      'name': 'Profile',
      'route': AppRoutes.profile,
      'icon': 'person',
      'description': 'Manage your profile',
    },
    {
      'name': 'Subscription',
      'route': AppRoutes.subscription,
      'icon': 'star',
      'description': 'Manage your subscription',
    },
    {
      'name': 'Settings',
      'route': AppRoutes.settings,
      'icon': 'settings',
      'description': 'App settings and preferences',
    },
    {
      'name': 'Accessibility',
      'route': AppRoutes.accessibility,
      'icon': 'accessibility',
      'description': 'Accessibility settings',
    },
  ];

  static const List<Map<String, dynamic>> support = [
    {
      'name': 'Help & FAQ',
      'route': AppRoutes.help,
      'icon': 'help',
      'description': 'Get help and find answers',
    },
  ];

  static Map<String, List<Map<String, dynamic>>> getAllCategories() {
    return {
      'Analytics': analytics,
      'Social': social,
      'Discover': discovery,
      'AI Features': aiFeatures,
      'Audio': audio,
      'Gamification': gamification,
      'Premium': premium,
      'Account': account,
      'Support': support,
    };
  }
}
