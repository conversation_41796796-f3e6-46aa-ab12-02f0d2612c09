import 'package:flutter/material.dart';
import 'app_theme.dart';

/// A cosmic-themed background widget that provides gradient backgrounds
/// for enhanced visual appeal and user engagement
class CosmicBackground extends StatelessWidget {
  final Widget child;
  final CosmicBackgroundType type;
  final bool animated;

  const CosmicBackground({
    super.key,
    required this.child,
    this.type = CosmicBackgroundType.primary,
    this.animated = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(gradient: _getGradient()),
      child: animated ? _AnimatedCosmicBackground(child: child) : child,
    );
  }

  LinearGradient _getGradient() {
    switch (type) {
      case CosmicBackgroundType.primary:
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.cosmicDeep,
            AppTheme.cosmicDark,
            AppTheme.cosmicMedium,
          ],
          stops: [0.0, 0.6, 1.0],
        );
      case CosmicBackgroundType.purple:
        return const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppTheme.cosmicDark,
            AppTheme.purpleDark,
            AppTheme.cosmicDeep,
          ],
          stops: [0.0, 0.5, 1.0],
        );
      case CosmicBackgroundType.accent:
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.purplePrimary,
            AppTheme.purpleSecondary,
            AppTheme.electricBlue,
          ],
          stops: [0.0, 0.7, 1.0],
        );
      case CosmicBackgroundType.subtle:
        return const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [AppTheme.cosmicSurface, AppTheme.cosmicDark],
        );
    }
  }
}

/// Animated version of the cosmic background with subtle movement
class _AnimatedCosmicBackground extends StatefulWidget {
  final Widget child;

  const _AnimatedCosmicBackground({required this.child});

  @override
  State<_AnimatedCosmicBackground> createState() =>
      _AnimatedCosmicBackgroundState();
}

class _AnimatedCosmicBackgroundState extends State<_AnimatedCosmicBackground>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppTheme.cosmicDeep,
                Color.lerp(
                  AppTheme.cosmicDark,
                  AppTheme.purpleDark,
                  _animation.value,
                )!,
                AppTheme.cosmicMedium,
              ],
              stops: [0.0, 0.6, 1.0],
            ),
          ),
          child: widget.child,
        );
      },
    );
  }
}

/// Types of cosmic backgrounds available
enum CosmicBackgroundType {
  /// Primary gradient from deep space to cosmic medium
  primary,

  /// Purple-focused gradient for accent areas
  purple,

  /// High-contrast accent gradient for special elements
  accent,

  /// Subtle gradient for cards and surfaces
  subtle,
}

/// Cosmic-themed card widget with gradient background
class CosmicCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final bool hasGlow;

  const CosmicCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.borderRadius = 16.0,
    this.hasGlow = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppTheme.cosmicSurface, AppTheme.cosmicMedium],
        ),
        boxShadow: hasGlow
            ? [
                BoxShadow(
                  color: AppTheme.purplePrimary.withValues(alpha: 0.3),
                  blurRadius: 20,
                  spreadRadius: 2,
                ),
              ]
            : null,
      ),
      child: Padding(
        padding: padding ?? const EdgeInsets.all(16.0),
        child: child,
      ),
    );
  }
}

/// Cosmic-themed button with gradient background
class CosmicButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final CosmicButtonType type;
  final IconData? icon;

  const CosmicButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.type = CosmicButtonType.primary,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        gradient: _getGradient(),
        boxShadow: type == CosmicButtonType.primary
            ? [
                BoxShadow(
                  color: AppTheme.purplePrimary.withValues(alpha: 0.4),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(25),
          onTap: isLoading ? null : onPressed,
          child: Center(
            child: isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.cosmicWhite,
                      ),
                    ),
                  )
                : Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (icon != null) ...[
                        Icon(icon, color: AppTheme.cosmicWhite, size: 20),
                        const SizedBox(width: 8),
                      ],
                      Text(text, style: AppTheme.buttonText),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  LinearGradient _getGradient() {
    switch (type) {
      case CosmicButtonType.primary:
        return const LinearGradient(
          colors: [AppTheme.purplePrimary, AppTheme.purpleSecondary],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case CosmicButtonType.secondary:
        return const LinearGradient(
          colors: [AppTheme.electricBlue, AppTheme.purplePrimary],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case CosmicButtonType.accent:
        return const LinearGradient(
          colors: [AppTheme.cosmicGold, AppTheme.warningOrange],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }
}

enum CosmicButtonType { primary, secondary, accent }
